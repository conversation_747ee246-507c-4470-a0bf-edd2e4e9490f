# frozen_string_literal: true

module Mockit
  module Mock
    GenericResponse = Struct.new(:body)

    module <PERSON>crolusApi
      def self.mock_create_book(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::OcrolusApi::BookCreationResult.new(
          response: response,
          book_id: Mockit::Store.current_mock_id
        )
      end

      def self.mock_upload_pdf(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::OcrolusApi::PdfUploadResult.new(
          response:,
          document_id: SecureRandom.hex(10)
        )
      end

      def self.mock_upload_image(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::OcrolusApi::ImageUploadResult.new(
          response:,
          image_group_pk: rand(1000)
        )
      end

      def self.mock_finalize_image_group(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::OcrolusApi::ImageGroupFinalizationResult.new(
          response:,
          mixed_doc_id: SecureRandom.hex(10)
        )
      end

      def self.mock_fetch_book_summary(overrides, super_method, *_args, **_kwargs)
        return super_method.call unless overrides['fetch_book_summary']

        json = JSON.parse(overrides['fetch_book_summary'])
        response = GenericResponse.new(json)
        Clients::OcrolusApi::BookSummaryResult.new(response:)
      end

      def self.mock_fetch_cash_flow_features(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::OcrolusApi::CashFlowFeaturesResult.new(response:)
      end

      def self.mock_fetch_enriched_transactions(overrides, super_method, *_args, **_kwargs)
        return super_method.call unless overrides['fetch_enriched_transactions']

        json = JSON.parse(overrides['fetch_enriched_transactions'])
        response = GenericResponse.new(json)
        Clients::OcrolusApi::EnrichedTransactionsResult.new(response:)
      end

      def self.mock_fetch_bank_statement_income(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::OcrolusApi::BankStatementIncomeResult.new(response:)
      end

      def self.mock_fetch_book_fraud_signals(overrides, super_method, *_args, **_kwargs)
        return super_method.call unless overrides['fetch_book_fraud_signals']

        json = JSON.parse(overrides['fetch_book_fraud_signals'])
        response = GenericResponse.new(json)
        Clients::OcrolusApi::BookFraudSignalsResult.new(response:)
      end
    end
  end
end
