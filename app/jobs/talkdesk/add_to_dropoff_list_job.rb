# frozen_string_literal: true

module Talkdesk
  class AddToDropoffListJob < ApplicationJob # rubocop:disable Metrics/ClassLength
    class InvalidDropoffStatusError < StandardError; end

    sidekiq_options queue: 'default', tags: %w[talkdesk application_dropoff], retry: 3

    TALKDESK_DEFAULT_TIMEZONE = 'America/Los_Angeles'
    TALKDESK_DUPLICATE_PHONE_NUMBER_ERROR_CODE = '0530930'
    TALKDESK_TIME_ZONE_NOT_FOUND_ERROR_CODE = '0530940'

    PRE_OFFER_STATUSES = %w[BASIC_INFO_COMPLETE ADD_INFO_COMPLETE].freeze
    POST_OFFER_STATUSES = %w[OFFERED PENDING].freeze
    APPROVED_STATUS = 'APPROVED'

    def perform(loan_id, tag, talkdesk_priority = nil)
      @loan_id = loan_id
      @tag = tag
      @talkdesk_priority = talkdesk_priority
      return notify_do_not_call if borrower_requested_do_not_call?
      return notify_cooldown if Talkdesk::InCooldown.call(loan_id:, phone_number:, loan_app_status:)

      remove_from_dropoff_list # remove before adding (to make Talkdesk dial again if number has already been contacted)
      update_older_queued_talkdesk_events
      talkdesk_record_id = add_to_dropoff_list
      create_talkdesk_event(talkdesk_record_id)
    end

    private

    attr_reader :loan_id, :tag, :talkdesk_priority

    def add_to_dropoff_list(timezone: nil, retry_count: 1)
      talkdesk_record_body = talkdesk_client.add_record_to_list(talkdesk_list_id, params.merge(timezone:), extra_data)
      Rails.logger.info('Loan added to Talkdesk dropoff list',
                        loan_id:, unified_id: loan.unified_id, request_id: loan.request_id, loan_app_status:,
                        talkdesk_list_id:, tracking_tag: tag, tags: %w[talkdesk dropoff_list])

      talkdesk_record_body.fetch('id', nil)
    rescue Clients::TalkdeskApi::BadRequestError => e
      handle_faraday_bad_request_error(e, retry_count)
    end

    def remove_from_dropoff_list
      delete_result = talkdesk_client.delete_record_from_list(talkdesk_list_id, params[:phone_number])
      Rails.logger.info('Loan removed from Talkdesk dropoff list before adding',
                        loan_id:, loan_app_status:, talkdesk_list_id:, delete_result:,
                        tracking_tag: tag, tags: %w[talkdesk dropoff_list])
    end

    def talkdesk_client
      @talkdesk_client ||= Clients::TalkdeskApi.new
    end

    def params
      @params ||= {
        first_name: loan.borrower.first_name,
        last_name: loan.borrower.last_name,
        phone_number:,
        priority: talkdesk_priority
      }.compact
    end

    def extra_data
      {
        activation_code: loan.code,
        debt_resolution_provider: service_entity_name,
        unified_id: loan.unified_id,
        credit_freeze_active: borrower_has_active_credit_freeze?,
        loan_app_status:,
        tag:
      }
    end

    def handle_faraday_bad_request_error(exception, retry_count)
      error_code = exception.response_body['code']

      if error_code == TALKDESK_DUPLICATE_PHONE_NUMBER_ERROR_CODE
        log_bad_request(exception, 'Ignored duplicate phone number error')
      elsif error_code == TALKDESK_TIME_ZONE_NOT_FOUND_ERROR_CODE && retry_count.positive?
        log_bad_request(exception, "Retrying Talkdesk call with a default timezone (#{TALKDESK_DEFAULT_TIMEZONE})")
        add_to_dropoff_list(timezone: TALKDESK_DEFAULT_TIMEZONE, retry_count: retry_count - 1)
      else
        log_bad_request(exception, 'Raising exception')
        raise exception
      end
    end

    def log_bad_request(exception, action_taken)
      Rails.logger.error('Loan rejected by Talkdesk during dropoff list addition',
                         loan_id:, request_id: loan.request_id, response_status: exception.response_status,
                         response_body: exception.response_body, action_taken:, talkdesk_list_id:, tracking_tag: tag,
                         tags: %w[talkdesk dropoff_list])
    end

    def update_older_queued_talkdesk_events
      TalkdeskEvent
        .where(loan:, disposition: TalkdeskEvent::QUEUED_DISPOSITION, talkdesk_list_id:)
        .find_each do |event|
          event.update!(disposition: TalkdeskEvent::QUEUE_REMOVED_DISPOSITION)
        end
    end

    def create_talkdesk_event(talkdesk_record_id)
      TalkdeskEvent.create!(
        loan:,
        direction: TalkdeskEvent::OUTBOUND_DIRECTION,
        disposition: TalkdeskEvent::QUEUED_DISPOSITION,
        enqueued_at: Time.zone.now,
        loan_app_status: loan.loan_app_status.name,
        phone_number: params[:phone_number],
        talkdesk_list_id:,
        talkdesk_record_id:
      )
    rescue ActiveRecord::ActiveRecordError => e
      handle_talkdesk_event_exception(e)
    end

    # We are not raising an exception here to avoid job retries
    # that would subsequently fail due to Talkdesk returning a 400 on
    # duplicate phone numbers.
    def handle_talkdesk_event_exception(exception)
      Rails.logger.error('Error creating TalkdeskEvent',
                         tags: %w[talkdesk dropoff_list], unified_id: loan.unified_id, loan_id:,
                         loan_app_status: loan.loan_app_status.name, tracking_tag: tag, exception:)
    end

    def borrower_requested_do_not_call?
      loan&.borrower&.do_not_call_requested_at.present?
    end

    def notify_do_not_call
      meta = { loan_id:, tag:, talkdesk_priority: }
      notify('add_to_dropoff_list_job', { success: false, fail_reason: 'do not call', meta: })
    end

    def notify_cooldown
      meta = { loan_id:, tag:, talkdesk_priority: }
      notify('add_to_dropoff_list_job', { success: false, fail_reason: 'in cooldown', meta: })
    end

    def loan
      @loan ||= ::Loan.includes(:loan_app_status, :loan_detail)
                      .joins(borrower: :borrower_additional_info)
                      .where(id: loan_id)
                      .order('loans.created_at DESC')
                      .first
    end

    def loan_app_status
      loan&.loan_app_status&.name
    end

    def phone_number
      loan&.actual_borrower_additional_info&.phone_number
    end

    def service_entity_name
      lead&.service_entity_name
    end

    def lead
      @lead ||= Lead.select(:service_entity_name).with_code(loan.code).first
    end

    def talkdesk_list_id
      @talkdesk_list_id ||= dropoff_list_map[service_entity_name.to_sym]
    end

    def dropoff_list_map
      # NOTE: Currently, Ops does not make calls to loan applications in pre-offer status.
      #       As a result, applications with an active credit freeze are added to the post-offer
      #       list to ensure that borrowers with a credit freeze are still contacted.
      return config[:post_offer_dropoff_list_map] if borrower_has_active_credit_freeze?

      case loan_app_status
      when *PRE_OFFER_STATUSES
        config[:pre_offer_dropoff_list_map]
      when *POST_OFFER_STATUSES
        config[:post_offer_dropoff_list_map]
      when APPROVED_STATUS
        config[:approved_dropoff_list_map]
      else
        raise InvalidDropoffStatusError, "Not a valid drop off status: #{loan_app_status}"
      end
    end

    def borrower_has_active_credit_freeze?
      loan.loan_detail.credit_freeze_active
    end

    def config
      Rails.application.config_for(:talkdesk_api)
    end
  end
end
