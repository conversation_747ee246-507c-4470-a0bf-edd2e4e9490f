# frozen_string_literal: true

# == Schema Information
#
# Table name: landing_leads
#
#  id                  :uuid             not null, primary key
#  email               :string
#  first_name          :string(255)
#  last_name           :string(255)
#  lead_code           :string(50)
#  phone_number        :string(20)
#  privacy_accepted_at :datetime
#  service_entity_name :string(50)
#  tcpa_accepted_at    :datetime
#  url                 :string(255)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
# Indexes
#
#  index_landing_leads_on_created_at       (created_at)
#  index_landing_leads_on_upper_lead_code  (upper((lead_code)::text))
#
class LandingLead < ApplicationRecord
  VALID_SERVICE_ENTITY_NAMES = [
    Constants::ServiceEntityNames::BEYOND_FINANCE,
    Constants::ServiceEntityNames::FIVE_LAKES_LAW_GROUP
  ].freeze

  before_validation :normalize_phone_number
  before_validation :sanitize_name_fields

  scope :with_lead_code, ->(lead_code) { where('UPPER(lead_code) = UPPER(?)', lead_code).order(created_at: :desc) }

  validates :phone_number, format: { with: /\A[0-9]{10}\z/, message: 'must be a 10-digit US phone number' },
                           allow_nil: true
  validates :url, presence: true
  validates :service_entity_name, inclusion: { in: VALID_SERVICE_ENTITY_NAMES, allow_nil: true }

  private

  def normalize_phone_number
    return if phone_number.nil?

    # Ensure phone_number is a string, strip '+1' or '1' prefix, and remove any dashes
    self.phone_number = phone_number.to_s.gsub(/\A(\+1|1)/, '').delete('-')
  end

  def sanitize_name_fields
    first_name&.strip!&.gsub!(/[^(a-zA-ZÀ-ÿ\-'. )]/, '')
    last_name&.strip!&.gsub!(/[^(a-zA-ZÀ-ÿ\-'. )]/, '')
  end
end
