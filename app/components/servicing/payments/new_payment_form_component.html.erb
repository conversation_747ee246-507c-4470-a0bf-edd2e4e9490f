<%= form_with(model: form_model, url: servicing_payments_path) do |form| %>
  <fieldset data-controller="servicing--payment-form">
    <legend class="sr-only">New Payment Form Attributes</legend>

    <div class="max-w-80 mb-6">
      <%= render UI::InputDateComponent.new(
            form:,
            field: :payment_date,
            label: 'Payment Date',
            autocomplete: 'off',
            placeholder: 'MM/DD/YYYY',
            testid: 'paymentDate-input',
            data: {
              'servicing--payment-form-target': 'dateInput',
              'min-date': form.object.min_payment_date.to_s,
              'min-datetime': form.object.min_payment_datetime.iso8601,
              'max-date': form.object.max_payment_date.to_s,
              'max-datetime': form.object.max_payment_datetime.iso8601
            },
            required_label: true,
            min: form.object.min_payment_date,
            max: form.object.max_payment_date
          ) %>
    </div>

    <div class="mb-6">
      <%= render Servicing::Payments::PaymentAmountFieldComponent.new(form:) %>
    </div>

    <div class="max-w-80 mb-6">
      <%= render UI::InputTextComponent.new(
            form:,
            field: :bank_account,
            label: 'Bank Account',
            testid: 'bankAccount-input',
            placeholder: 'Bank Account Required',
            required: true,
            disabled: true
          ) %>

      <%= if form.object.errors[:bank_account].blank?
            render UI::ErrorComponent.new(
              error: "To change accounts, call us at #{service_entity_phone_number(service_entity,
                                                                                   class: 'hover:underline')}",
              field: 'info_bank_account'
            )
          end %>
    </div>

    <%= form.hidden_field :payment_profile_id %>
    <%= form.hidden_field :payment_time_zone, data: { 'servicing--payment-form-target': 'timezoneInput' } %>

    <div class="my-8">
      <p class="text-xs text-brand-gray-800 leading-normal">
        By clicking "Schedule Payment" on <time data-servicing--payment-form-target="todayLabel"><%= today %></time>,
        I, <%= form.object.borrower_name.titleize %>, authorize Above Lending, Inc. ("Above"), its affiliates, and any
        third parties that may be providing services to me on behalf of Above to initiate a single-entry ACH transfer
        from the Bank Account for the Payment Amount on the Payment Date. You can call us at
        <%= service_entity_phone_number(service_entity, class: 'font-semibold hover:underline') %>
        to confirm whether the transfer has occurred. If you wish to cancel your ACH authorization, please call us at
        <%= service_entity_phone_number(service_entity, class: 'font-semibold hover:underline') %>
        Monday through Friday, 8am to 7pm CT, at least 2 business days prior to your Payment Date.
      </p>
    </div>

    <%= render UI::SubmitComponent.new(form:).with_content('Schedule Payment') %>
  </fieldset>

  <% if form_model.show_duplicate_warning_modal? %>
    <%= render UI::ModalComponent.new(load_open: true, force_open: true) do |c| %>

      <%= c.with_body do %>
        <div class="max-w-md mx-auto pt-6">
          <h3 class="text-3xl text-center font-semibold mb-6">Please Confirm</h3>

          <p class="text-center">
            You recently scheduled a payment for the same amount and processing date. Please confirm you would like to schedule another payment for the same amount and date.
          </p>
        </div>
      <% end %>

      <%= c.with_action do %>
        <div class="max-w-md pb-6 text-center">
          <div class="mb-2">
            <%= render UI::ButtonComponent.new(
                  href: servicing_dashboard_index_path,
                  testid: 'no-view-scheduled-payments',
                  additional_classes: ['w-auto']
                ).with_content('No, View Scheduled Payments') %>
          </div>

          <%= form.hidden_field :override_duplicate_warning, value: true %>

          <%= render UI::SubmitComponent.new(
                form:,
                testid: 'yes-continue-making-payment',
                variant: :outline,
                additional_classes: ['w-auto']
              ).with_content('Yes, Continue Making Payment') %>
        </div>
      <% end %>
    <% end %>
  <% end %>
<% end %>
