# frozen_string_literal: true

module LoanApplications
  class Resolver < Service::Base
    attr_accessor :landing_lead
    attr_reader :loan, :lead

    delegate :basic_info_complete?, :add_info_complete?, to: :loan, allow_nil: true

    attribute :code
    attribute :loan_id

    def initialize(...)
      super

      resolve_lead
      resolve_landing_lead
      resolve_loan
    end

    def self.with_landing_lead(landing_lead)
      instance = new(code: landing_lead.lead_code)
      instance.landing_lead = landing_lead

      instance
    end

    def activated_account?
      return @activated_account if defined?(@activated_account)

      return @activated_account = false if email_address.blank?

      @activated_account = Users::CheckAccountActivation.call(email: email_address)
    end

    def email_address
      loan&.borrower&.email || landing_lead_email
    end

    def credit_freeze_active?
      loan&.loan_detail&.credit_freeze_active.present?
    end

    def landing_lead_id
      landing_lead&.id
    end

    def landing_lead_email
      landing_lead&.email
    end

    def lead_eligible?
      lead&.code_status == 'valid'
    end

    def lead_id
      lead&.id
    end

    def loan_active?
      return @loan_active if defined?(@loan_active)
      return @loan_active = false unless loan.present?

      @loan_active = loan.active?
    end

    def loan_bank_accounts
      loan&.bank_accounts
    end

    def loan_has_enabled_bank_account?
      loan&.borrower&.bank_account&.enabled
    end

    def loan_app_status
      loan&.loan_app_status&.name
    end

    def withdraw_loan?
      return false unless loan.present?
      return false if activated_account?
      return false if loan.approved?
      return false unless loan.basic_info_complete? || loan.add_info_complete?

      true
    end

    def contract_generated?
      loan&.loanpro_loan.present?
    end

    private

    def resolve_lead
      return if defined?(@lead) || code.blank?

      @lead = Lead.with_code(code).first
    end

    def resolve_landing_lead
      return if defined?(@landing_lead) || code.blank?

      @landing_lead = LandingLead.with_lead_code(code)&.first
    end

    def resolve_loan
      @loan = ::Loan.find_by(id: loan_id)
      @loan ||= resolve_loan_from_email
      @loan ||= resolve_loan_from_code

      self.loan_id = @loan.id if @loan
    end

    def resolve_loan_from_email
      arel_table = Borrower.arel_table
      borrower = Borrower.find_by(arel_table[:email].matches(landing_lead_email))

      ::Loan.latest_active_for_borrower(borrower).first if borrower.present?
    end

    def resolve_loan_from_code
      ::Loan.with_code(code).where(product_type: Loan::IPL_LOAN_PRODUCT_TYPE,
                                   deleted_at: nil).order(created_at: :desc).first
    end
  end
end
