import { Controller } from "@hotwired/stimulus";

const toYYYYmmdd = (date) => {
  return [
    date.getFullYear(),
    `0${date.getMonth() + 1}`.slice(-2),
    `0${date.getDate()}`.slice(-2)
  ].join('-');
}

export default class extends Controller {
  static targets = ['input', 'customPreset', 'custom', 'dateInput', 'timezoneInput', 'payoffLabel', 'todayLabel'];

  dateInputTargetConnected(element) {
    const now = new Date();
    const min = new Date(element.dataset.minDatetime);
    const minLocal = new Date(`${element.dataset.minDate} 00:00:00`);
    const max = new Date(element.dataset.maxDatetime);
    const maxLocal = new Date(`${element.dataset.maxDate} 23:59:59`);

    if (now < minLocal && now >= min) {
      // We are past midnight Central Time, but still on the previous day locally. Allow the day before the minimum.
      element.min = toYYYYmmdd(min);
    } else {
      element.min = toYYYYmmdd(minLocal);
    }

    if (now >= maxLocal && now < max) {
      // We are past midnight locally, but still on the previous day in Central Time. Allow the day after the maximum.
      element.max = toYYYYmmdd(max);
    } else {
      element.max = toYYYYmmdd(maxLocal);
    }

    if (!element.value) {
      element.value = element.min;
    }
  }

  timezoneInputTargetConnected(element) {
    element.value = Intl.DateTimeFormat().resolvedOptions().timeZone;
  }

  payoffLabelTargetConnected(element) {
    const today = new Date();
    element.innerText = today.toLocaleDateString('en-US')
  }

  todayLabelTargetConnected(element) {
    const today = new Date();
    element.innerText = today.toLocaleDateString('en-US', { dateStyle: 'medium' })
  }

  setPreset(event) {
    this.customTarget.blur();
    this.customTarget.value = "";
    this.inputTarget.value = event.target.dataset.value;
  }

  setCustomPreset() {
    this.customTarget.focus();
    this.inputTarget.value = this.customTarget.value;
  }

  changeCustom() {
    this.customPresetTarget.checked = true;
    this.inputTarget.value = this.customTarget.value;
  }
}
