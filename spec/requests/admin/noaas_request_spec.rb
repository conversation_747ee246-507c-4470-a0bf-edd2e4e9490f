# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'NOAAs', type: :request do
  let!(:loan) { create(:loan) }
  let!(:loan_status_history) { create(:loan_status_history, loan:, new_status: loan_status) }
  let(:loan_status) { 'BACKEND_DECLINED' }
  let(:unified_id) { loan.unified_id }
  let!(:noaa_document) { create(:doc, loan:, template: create(:doc_template, type: DocTemplate::TYPES[:CRB_AA])) }

  before { Aws.config.update(stub_responses: true) }

  describe '#index' do
    it 'renders' do
      get admin_noaas_path

      expect(response).to be_successful
    end
  end

  describe '#search' do
    it 'retrieves loans with associated noaas by unified id' do
      turbo_post path: admin_noaas_search_path, params: { admin_noaa_search_form_model: { unified_id: } }

      assert_select("turbo-stream[action='update'][target='noaa-search']") do
        assert_select('input#admin_noaa_search_form_model_unified_id', value: unified_id)
      end

      assert_select("turbo-stream[action='update'][target='noaa-results']") do
        assert_select("[data-testid='noaa-download']", href: noaa_document.uri)
      end
    end

    it 'resets the form' do
      turbo_post path: admin_noaas_search_path, params: { clear: true, admin_noaa_search_form_model: { unified_id: } }

      assert_select("turbo-stream[action='update'][target='noaa-search']") do
        assert_select('input#admin_noaa_search_form_model_unified_id', value: '')
      end

      assert_select("turbo-stream[action='update'][target='noaa-results']") do
        assert_select("[data-testid='noaa-download']", count: 0)
      end
    end
  end
end
