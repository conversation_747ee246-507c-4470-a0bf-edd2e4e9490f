# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Borrowers::AccountsController, type: :request do
  let!(:loan_inquiry) { create(:loan_inquiry) }
  let!(:loan_inquiry_expired) { create(:loan_inquiry_expired) }
  let!(:loan_inquiry_declined) { create(:loan_inquiry, :declined, offers: loan_inquiry.offers) }
  let!(:constent_doc_templates) do
    DocTemplate::UPL_CONSENT_DOCUMENTS.each do |type|
      create(:doc_template, name: type, type:)
    end
  end

  describe '#upl_account_new' do
    it 'with invalid loan_inquiry' do
      get upl_account_new_borrowers_path(loan_inquiry_id: 'invalid')

      expect(response.body).to include('Apply by phone to start a new application')
    end

    it 'with expired loan_inquiry' do
      get upl_account_new_borrowers_path(loan_inquiry_id: loan_inquiry_expired.id)

      expect(response.body).to include('Apply by phone to start a new application')
    end

    it 'with declined loan_inquiry' do
      get upl_account_new_borrowers_path(loan_inquiry_id: loan_inquiry_declined.id)

      expect(response.body).to include('we are unable to provide you with a loan')
    end

    it 'with valid loan_inquiry' do
      get upl_account_new_borrowers_path(loan_inquiry_id: loan_inquiry.id)

      expect(response.body).not_to include('Apply by phone to start a new application')
    end
  end

  describe '#upl_account_create' do
    let(:headers) do
      { 'X-Forwarded-For': client_ip, 'User-Agent': user_agent }
    end
    let(:identity_id) { SecureRandom.uuid }
    let!(:borrower) { create(:borrower, email:, identity_id:) }
    let(:client_ip) { Faker::Internet.ip_v4_address }
    let(:user_agent) { 'Testing User Agent' }

    let(:password) { Faker::Internet.password(min_length: 8, max_length: 20, mix_case: true, special_characters: true) }
    let(:email) { loan_inquiry.application['email'] }
    let(:valid_attributes) do
      { email:, password:, esign_consent: true, loan_inquiry_id: loan_inquiry.id }
    end
    let(:app_valid_attributes) { valid_attributes.except(:email) }

    let!(:session) { {} }

    before do
      mock_session!(session)
      allow(LoanApplications::AppFromInquiry).to receive(:call)
      allow(Users::CreateUser).to receive(:call).and_call_original
      allow(Documents::GeneratePdf).to receive(:call)
      allow(Clients::GdsApi).to receive(:save_selection).and_return({})
    end

    it 'returns partially completed forms on error' do
      attributes = valid_attributes.except(:password)

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: attributes }, headers: headers

      assert_select("turbo-stream[action='update'][target='create-account-form']") do
        assert_select('input#borrowers_create_account_form_model_email', value: valid_attributes[:email])
        assert_select('input#borrowers_create_account_form_model_esign_consent', value: valid_attributes[:esign_consent])
      end
    end

    it 'redirects to login with existing loan' do
      exception = LoanApplications::AppFromInquiry::ExistingLoanForEmail.new('')
      expect(LoanApplications::AppFromInquiry).to receive(:call).with(app_valid_attributes).and_raise(exception)

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: valid_attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: valid_attributes }, headers: headers

      expect(response).to redirect_to(signin_borrowers_path(message: 'ongoing_loan'))
    end

    it 'creates and authenticates the user' do
      expect(LoanApplications::AppFromInquiry).to receive(:call).with(app_valid_attributes).and_call_original

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: valid_attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: valid_attributes }, headers: headers

      expect(response).to redirect_to(resume_borrowers_path)
    end

    it 'shows error message when the user is unable to be authenticated with the provided credentials' do
      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: valid_attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: valid_attributes.merge(email: '<EMAIL>') }, headers: headers

      assert_select("turbo-stream[action='update'][target='create-account-form']") do
        assert_select('input#borrowers_create_account_form_model_email', value: valid_attributes[:email])
      end

      expect(response.body).to include('Incorrect username or password')
    end

    it 'shows error message when no email is specified' do
      expect(LoanApplications::AppFromInquiry).to receive(:call).with(app_valid_attributes).and_call_original

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: valid_attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: valid_attributes.merge(email: nil) }, headers: headers

      assert_select("turbo-stream[action='update'][target='create-account-form']") do
        assert_select('input#borrowers_create_account_form_model_email', value: valid_attributes[:email])
      end

      expect(response.body).to include('Incorrect username or password')
    end

    it 'shows error message on unexpected exception' do
      expect(LoanApplications::AppFromInquiry).to receive(:call).with(app_valid_attributes).and_raise('Boom!')

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: valid_attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: valid_attributes }, headers: headers

      assert_select("turbo-stream[action='update'][target='create-account-form']") do
        assert_select('input#borrowers_create_account_form_model_email', value: valid_attributes[:email])
      end

      expect(response.body).to include('An error has occurred. Please refresh the page and try again.')
    end

    it 'shows expiry view if inquiry expired while form was already loaded' do
      attributes = valid_attributes.merge(loan_inquiry_id: loan_inquiry_expired.id)

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: attributes }, headers: headers

      expect(response.body).to include('Apply by phone to start a new application')
    end

    it 'shows AA view if inquiry was declined while form was already loaded' do
      attributes = valid_attributes.merge(loan_inquiry_id: loan_inquiry_declined.id)

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: attributes }, headers: headers

      expect(response.body).to include('we are unable to provide you with a loan')
    end

    it 'redirects to the next funnel step' do
      expect(LoanApplications::AppFromInquiry).to receive(:call).with(app_valid_attributes).and_call_original

      turbo_post path: upl_account_create_borrowers_path(loan_inquiry_id: valid_attributes[:loan_inquiry_id]), params: { borrowers_create_account_form_model: valid_attributes }, headers: headers

      expect(session[:borrower_id]).to eq(borrower.id)
      expect(session[:code]).to eq(borrower.loan.code)

      expect(response).to redirect_to(resume_borrowers_path)
    end
  end

  describe '#account_setup' do
    let(:valid_code) { 'val123' }
    let(:invalid_code) { 'inv123' }
    let!(:landing_lead) { create(:landing_lead, lead_code: valid_code) }
    let!(:borrower) { create(:borrower, email: landing_lead.email) }
    let!(:loan) { create(:loan, :offered, code: valid_code, borrower:) }

    it 'redirects to basic info with no loan' do
      loan.destroy!

      query = { offer: valid_code, s: 'bf' }

      get account_setup_borrowers_path(query)

      expect(response).to redirect_to(basic_info_loan_applications_path(query))
    end

    it 'redirects to intake with no borrower email' do
      query = { offer: invalid_code, s: 'bf' }

      get account_setup_borrowers_path(query)

      expect(response).to redirect_to(intake_loan_applications_path(query))
    end

    it 'renders' do
      query = { offer: valid_code, s: 'bf' }

      get account_setup_borrowers_path(query)

      expect(response).to render_template(:account_setup)

      expect_request_event_record

      expect(request.session[:code]).to eq(valid_code)
      expect(request.session[:service_entity]).to eq('bf')
    end
  end

  describe '#resend_welcome_email' do
    let(:query) { { offer: 'Wv1F5Q', s: 'bf' } }
    let(:session) { { code: 'Wv1F5Q', service_entity: 'bf' } }
    let!(:landing_lead) { create(:landing_lead, lead_code: session[:code]) }
    let!(:user) { create(:user, :with_additional_info, email: landing_lead.email, borrower:) }
    let!(:borrower) { create(:borrower, email: landing_lead.email) }

    before do
      mock_session!(session)
      allow(Clients::CommunicationsServiceApi).to receive(:send_message!)
    end

    it 'redirects to whoops on error' do
      allow_any_instance_of(Users::SendWelcomeEmail).to receive(:call).and_raise(StandardError.new('Boom!'))

      turbo_post path: resend_welcome_email_borrowers_path

      expect_request_event_record

      expect(response).to redirect_to(whoops_exit_pages_path(query))
      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end

    it 'triggers another welcome email to the correct user and renders a notification' do
      turbo_post path: resend_welcome_email_borrowers_path

      expect(response).to be_successful

      expect(Clients::CommunicationsServiceApi).to have_received(:send_message!).with(hash_including(recipient: borrower.email))

      assert_select("turbo-stream[action='update'][target='notification']") do
        assert_select('*', text: /Please open your email and click the “Create Your Account” button to activate your account/)
      end
    end

    context 'with multiple different borrowers' do
      # NOTE: The sequence was replicated from a production scenario that resulted in a Whoops error.
      # It could probably be simplified, but it's a complex edge case enough that it's worth keeping
      # as is to catch anypossible regressions in the loan applications resolver.

      let!(:email1) { Faker::Internet.email }
      let!(:email2) { Faker::Internet.email }

      # These represent 6 submissions on the intake form at different times, with 2 different emails.
      let!(:landing_lead) { create(:landing_lead, lead_code: session[:code], email: email1, created_at: Time.parse('2025-02-26 17:40:53.477 Z')) }
      let!(:landing_lead2) { create(:landing_lead, lead_code: session[:code], email: email1, created_at: Time.parse('2025-04-02 15:49:52.855 Z')) }
      let!(:landing_lead3) { create(:landing_lead, lead_code: session[:code], email: email2, created_at: Time.parse('2025-07-01 10:09:14.450 Z')) }
      let!(:landing_lead4) { create(:landing_lead, lead_code: session[:code], email: email2, created_at: Time.parse('2025-07-07 15:32:08.641 Z')) }
      let!(:landing_lead5) { create(:landing_lead, lead_code: session[:code], email: email2, created_at: Time.parse('2025-07-08 12:38:12.862 Z')) }
      let!(:landing_lead6) { create(:landing_lead, lead_code: session[:code], email: email1, created_at: Time.parse('2025-07-08 12:38:41.025 Z')) }

      # These represent the 2 borrowers created from the 2 different emails, at different times.
      let!(:borrower) { create(:borrower, email: email2, created_at: Time.parse('2025-02-18 22:56:18.047 Z')) }
      let!(:borrower2) { create(:borrower, email: email1, created_at: Time.parse('2025-04-02 15:50:21.845 Z')) }

      # These represent the 2 users created from the 2 borrowers above.
      let!(:user) { create(:user, email: email2, created_at: Time.parse('2025-02-18 22:56:18.047 Z'), borrower: borrower) }
      let!(:user2) { create(:user, email: email1, created_at: Time.parse('2025-04-02 15:50:21.845 Z'), borrower: borrower2) }

      # These represent the 3 loans created from the various intake submissions over time.
      let!(:loan) { create(:ipl_loan, :expired, code: session[:code], borrower: borrower, created_at: Time.parse('2025-02-18 22:56:18.058 +0000')) }
      let!(:loan2) { create(:ipl_loan, :expired, code: session[:code], borrower: borrower2, created_at: Time.parse('2025-04-02 15:50:22.177 +0000')) }
      let!(:loan3) { create(:ipl_loan, :offered, code: session[:code], borrower: borrower, created_at: Time.parse('2025-07-01 10:09:29.516 +0000')) }

      it 'triggers another welcome email to the correct user and renders a notification' do
        turbo_post path: resend_welcome_email_borrowers_path

        expect(response).to be_successful

        # Since we have one active loan at the moment (loan3) we expect the welcome message to be successfully sent to its borrower email.
        expect(Clients::CommunicationsServiceApi).to have_received(:send_message!).with(hash_including(recipient: borrower.email))

        assert_select("turbo-stream[action='update'][target='notification']") do
          assert_select('*', text: /Please open your email and click the “Create Your Account” button to activate your account/)
        end
      end
    end
  end
end
