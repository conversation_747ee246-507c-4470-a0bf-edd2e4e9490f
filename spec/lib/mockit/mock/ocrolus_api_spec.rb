# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Mockit::Mock::OcrolusApi do
  let(:service_key) { 'clients/ocrolus_api' }

  subject(:ocrolus_api) do
    Class.new(Clients::OcrolusApi) do
      class << self
        %i[
          create_book upload_pdf upload_image finalize_image_group
          fetch_book_summary fetch_cash_flow_features fetch_enriched_transactions
          fetch_bank_statement_income fetch_book_fraud_signals
        ].each do |meth|
          define_method(meth) do |*_args, **_kwargs|
            meth
          end
        end
      end
    end
  end

  before do
    allow(subject).to receive(:name).and_return('Clients::OcrolusApi')
    Mockit.mock_classes(subject => Mockit::Mock::OcrolusA<PERSON>)
  end

  after do
    RequestStore.clear!
  end

  describe 'mocked method' do
    %i[
      create_book upload_pdf upload_image finalize_image_group
      fetch_book_summary fetch_cash_flow_features fetch_enriched_transactions
      fetch_bank_statement_income fetch_book_fraud_signals
    ].each do |meth|
      it "calls original #{meth} if no mock is set" do
        expect(subject.send(meth)).to eq(meth)
      end
    end
  end

  context 'with mock set' do
    let(:default_overrides) do
      {
        fetch_book_summary: { a: 'b' }.to_json,
        fetch_enriched_transactions: { b: 'c' }.to_json,
        fetch_book_fraud_signals: { c: 'd' }.to_json
      }
    end

    def without_override(key)
      default_overrides.dup.tap { |h| h.delete(key) }
    end

    before do
      Mockit::Store.mock_id = 1
      Mockit::Store.write(service: service_key, overrides: default_overrides)
    end

    it '#create_book' do
      result = ocrolus_api.create_book
      expect(result).to be_instance_of(Clients::OcrolusApi::BookCreationResult)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.book_id).to eq('1')
    end

    it '#upload_pdf' do
      result = ocrolus_api.upload_pdf
      expect(result).to be_instance_of(Clients::OcrolusApi::PdfUploadResult)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.document_id).to be_a(String)
    end

    it '#upload_image' do
      result = ocrolus_api.upload_image
      expect(result).to be_instance_of(Clients::OcrolusApi::ImageUploadResult)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.image_group_pk).to be_a(Integer)
    end

    it '#finalize_image_group' do
      result = ocrolus_api.finalize_image_group
      expect(result).to be_instance_of(Clients::OcrolusApi::ImageGroupFinalizationResult)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.mixed_doc_id).to be_a(String)
    end

    context '#fetch_book_summary' do
      it 'has specific fetch_book_summary overrides' do
        result = ocrolus_api.fetch_book_summary(1)
        expect(result.response).to eq(Mockit::Mock::GenericResponse.new(JSON.parse(default_overrides[:fetch_book_summary])))
      end

      it 'without specific override falls back to original' do
        Mockit::Store.write(service: service_key, overrides: without_override(:fetch_book_summary))
        result = ocrolus_api.fetch_book_summary(1)
        expect(result).to eq(:fetch_book_summary)
      end
    end

    it '#fetch_cash_flow_features' do
      result = ocrolus_api.fetch_cash_flow_features
      expect(result).to be_instance_of(Clients::OcrolusApi::CashFlowFeaturesResult)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
    end

    context '#fetch_enriched_transactions' do
      it 'has specific fetch_enriched_transactions overrides' do
        result = ocrolus_api.fetch_enriched_transactions(1)
        expect(result.response).to eq(Mockit::Mock::GenericResponse.new(JSON.parse(default_overrides[:fetch_enriched_transactions])))
      end

      it 'without specific override falls back to original' do
        Mockit::Store.write(service: service_key, overrides: without_override(:fetch_enriched_transactions))
        result = ocrolus_api.fetch_enriched_transactions(1)
        expect(result).to eq(:fetch_enriched_transactions)
      end
    end

    it '#fetch_bank_statement_income' do
      result = ocrolus_api.fetch_bank_statement_income
      expect(result).to be_instance_of(Clients::OcrolusApi::BankStatementIncomeResult)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
    end

    context '#fetch_book_fraud_signals' do
      it 'has specific fetch_book_fraud_signals overrides' do
        result = ocrolus_api.fetch_book_fraud_signals(1)
        expect(result.response).to eq(Mockit::Mock::GenericResponse.new(JSON.parse(default_overrides[:fetch_book_fraud_signals])))
      end

      it 'without specific override falls back to original' do
        Mockit::Store.write(service: service_key, overrides: without_override(:fetch_book_fraud_signals))
        result = ocrolus_api.fetch_book_fraud_signals(1)
        expect(result).to eq(:fetch_book_fraud_signals)
      end
    end
  end
end
