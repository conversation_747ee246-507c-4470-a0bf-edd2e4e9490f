# frozen_string_literal: true

require 'rails_helper'

VALID_ADD_TO_DROPOFF_STATUSES = %w[BASIC_INFO_COMPLETE ADD_INFO_COMPLETE OFFERED PENDING APPROVED].freeze

RSpec.describe Talkdesk::AddToDropoffListJob do
  include ActiveSupport::Testing::TimeHelpers

  shared_examples 'talkdesk dropoff list management' do
    it 'calls Talkdesk API to delete and re-add to the status-appropriate Beyond Finance dropoff list' do
      expect(expected_talkdesk_id).to be_present # sanity check in case dropoff list maps are misconfigured
      loan.update(updated_at: 6.minutes.ago)

      subject.perform(loan.id, tracking_tag, talkdesk_priority)

      expect(talkdesk_api_double).to have_received(:delete_record_from_list).with(expected_talkdesk_id, expected_params[:phone_number])
      expect(Rails.logger).to have_received(:info)
        .with('Loan removed from Talkdesk dropoff list before adding', hash_including(loan_id: loan.id, delete_result: true))
      expect(talkdesk_api_double).to have_received(:add_record_to_list).with(expected_talkdesk_id, expected_params, expected_extra_data)
      expect(Rails.logger).to have_received(:info)
        .with('<PERSON>an added to Talkdesk dropoff list',
              loan_id: loan.id,
              loan_app_status: loan.loan_app_status.name,
              unified_id: loan.unified_id,
              request_id: loan.request_id,
              talkdesk_list_id: expected_talkdesk_id,
              tracking_tag:,
              tags: %w[talkdesk dropoff_list])
    end

    it 'calls Talkdesk API to delete and re-add to the status-appropriate Five Lakes dropoff list' do
      expect(expected_talkdesk_id).to be_present # sanity check in case dropoff list maps are misconfigured
      lead.update(service_entity_name: 'Five Lakes Law Group')
      loan.update(updated_at: 6.minutes.ago)

      subject.perform(loan.id, tracking_tag, talkdesk_priority)

      expect(talkdesk_api_double).to have_received(:delete_record_from_list).with(expected_talkdesk_id, expected_params[:phone_number])
      expect(Rails.logger).to have_received(:info)
        .with('Loan removed from Talkdesk dropoff list before adding', hash_including(loan_id: loan.id, delete_result: true))
      expect(talkdesk_api_double).to have_received(:add_record_to_list).with(expected_talkdesk_id, expected_params, expected_extra_data)
      expect(Rails.logger).to have_received(:info)
        .with('Loan added to Talkdesk dropoff list',
              loan_id: loan.id,
              loan_app_status: loan.loan_app_status.name,
              unified_id: loan.unified_id,
              request_id: loan.request_id,
              talkdesk_list_id: expected_talkdesk_id,
              tracking_tag:,
              tags: %w[talkdesk dropoff_list])
    end

    context 'when previous TalkdeskEvents with QUEUED disposition exist for the loan' do
      let(:previous_talkdesk_event_count) { 5 }

      before do
        create_list(:talkdesk_event,
                    previous_talkdesk_event_count,
                    loan:,
                    direction: TalkdeskEvent::OUTBOUND_DIRECTION,
                    disposition: TalkdeskEvent::QUEUED_DISPOSITION,
                    enqueued_at: '2024-02-01'.to_datetime,
                    talkdesk_list_id: expected_talkdesk_id)
      end

      it 'updates disposition status on all of them' do
        subject.perform(loan.id, tracking_tag)

        talkdesk_events_with_queued_disposition =
          TalkdeskEvent.where(loan:, talkdesk_list_id: expected_talkdesk_id,
                              disposition: TalkdeskEvent::QUEUED_DISPOSITION)

        expect(talkdesk_events_with_queued_disposition.count).to eq(1)

        talkdesk_events_with_queue_removed_disposition =
          TalkdeskEvent.where(loan:, talkdesk_list_id: expected_talkdesk_id,
                              disposition: TalkdeskEvent::QUEUE_REMOVED_DISPOSITION)

        expect(talkdesk_events_with_queue_removed_disposition.count).to eq(previous_talkdesk_event_count)
      end
    end

    it 'creates a TalkdeskEvent' do
      travel_to('2024-03-01'.to_datetime)

      expect(expected_talkdesk_id).to be_present
      loan.update(updated_at: 6.minutes.ago)

      subject.perform(loan.id, tracking_tag)
      talkdesk_event = TalkdeskEvent.last
      expect(talkdesk_event.loan).to eq(loan)
      expect(talkdesk_event.talkdesk_list_id).to eq(expected_talkdesk_id)
      expect(talkdesk_event.phone_number).to eq(borrower_additional_info.phone_number)
      expect(talkdesk_event.talkdesk_record_id).to eq(talkdesk_record_id)
      expect(talkdesk_event.direction).to eq(TalkdeskEvent::OUTBOUND_DIRECTION)
      expect(talkdesk_event.disposition).to eq(TalkdeskEvent::QUEUED_DISPOSITION)
      expect(talkdesk_event.enqueued_at).to eq('2024-03-01'.to_datetime)
    end

    context 'when the borrower has requested not to be called' do
      before do
        borrower.update!(do_not_call_requested_at: Time.zone.now)
      end

      it 'does not call Talkdesk' do
        expect do
          subject.perform(loan.id, tracking_tag, talkdesk_priority)
        end.not_to change(TalkdeskEvent, :count)

        meta = { loan_id: loan.id, tag: tracking_tag, talkdesk_priority: }
        expect_to_notify('add_to_dropoff_list_job', success: false, fail_reason: 'do not call', meta:)

        expect(talkdesk_api_double).not_to have_received(:delete_record_from_list)
        expect(talkdesk_api_double).not_to have_received(:add_record_to_list)
      end
    end
  end

  describe '#perform' do
    let(:talkdesk_api_double) { instance_double(Clients::TalkdeskApi) }
    let(:talkdesk_record_id) { SecureRandom.uuid }

    before do
      allow(Clients::TalkdeskApi).to receive(:new).and_return(talkdesk_api_double)
      allow(talkdesk_api_double).to receive(:add_record_to_list).and_return({ 'id' => talkdesk_record_id })
      allow(talkdesk_api_double).to receive(:delete_record_from_list).and_return(true)

      allow(Rails.logger).to receive(:info).and_call_original
      allow(Rails.logger).to receive(:error).and_call_original

      allow(Talkdesk::InCooldown).to receive(:call).and_return(false)

      set_notifier_stubs
    end

    context 'when there is an associated loan application' do
      # Get LoanAppStatus id for status name
      def app_status_for(status_name)
        LoanAppStatus.for(status_name)
      end

      def expected_talkdesk_id
        entity_name = lead.service_entity_name.to_sym
        Rails.application.config_for(:talkdesk_api).dig(dropoff_list_map_key, entity_name)
      end

      def dropoff_list_map_key
        case loan_app_status.name
        when 'BASIC_INFO_COMPLETE', 'ADD_INFO_COMPLETE' then :pre_offer_dropoff_list_map
        when 'OFFERED', 'PENDING' then :post_offer_dropoff_list_map
        when 'APPROVED' then :approved_dropoff_list_map
        end
      end

      def expected_params
        {
          first_name: borrower.first_name,
          last_name: borrower.last_name,
          phone_number: borrower_additional_info.phone_number,
          priority: talkdesk_priority,
          timezone: nil
        }
      end

      def expected_extra_data
        {
          activation_code: loan.code,
          debt_resolution_provider: lead.service_entity_name,
          unified_id: loan.unified_id,
          loan_app_status: loan.loan_app_status.name,
          credit_freeze_active: loan_detail.credit_freeze_active,
          tag: tracking_tag
        }
      end

      let!(:borrower) { create(:borrower) }
      let!(:borrower_additional_info) { create(:borrower_additional_info, borrower:, loan:) }
      let!(:loan) { create(:loan, borrower:, loan_app_status:, source_type: 'WEB') }
      let!(:loan_detail) { create(:loan_detail, loan:, credit_freeze_active: false) }
      let!(:lead) { create(:lead, code: loan.code, service_entity_name: 'Beyond Finance') }
      let(:loan_app_status) { app_status_for('PENDING') }
      let(:tracking_tag) { 'day_0' }
      let(:talkdesk_priority) { rand(1..10) }

      it 'ignores attempted calls when the borrower should be in cooldown' do
        expect(Talkdesk::InCooldown).to receive(:call).with(loan_id: loan.id, phone_number: borrower_additional_info.phone_number, loan_app_status: loan_app_status.name).and_return(true)

        expect do
          subject.perform(loan.id, tracking_tag, talkdesk_priority)
        end.not_to change(TalkdeskEvent, :count)

        meta = { loan_id: loan.id, tag: tracking_tag, talkdesk_priority: }
        expect_to_notify('add_to_dropoff_list_job', success: false, fail_reason: 'in cooldown', meta:)

        expect(talkdesk_api_double).not_to have_received(:delete_record_from_list)
        expect(talkdesk_api_double).not_to have_received(:add_record_to_list)
      end

      it 'picks the right phone number based on the composite key' do
        borrower_additional_info.update!(loan: create(:loan, borrower:, loan_app_status:, source_type: 'WEB'), phone_number: '1234567890')
        create(:borrower_additional_info, borrower:, loan:, phone_number: '0987654321')

        expect(Talkdesk::InCooldown).to receive(:call).with(loan_id: loan.id, phone_number: '0987654321', loan_app_status: loan_app_status.name).and_return(true)
        expect { subject.perform(loan.id, tracking_tag, talkdesk_priority) }.not_to change(TalkdeskEvent, :count)
      end

      VALID_ADD_TO_DROPOFF_STATUSES.each do |status_name|
        context "when loan app status is #{status_name}" do
          let(:loan_app_status) { app_status_for(status_name) }

          include_examples 'talkdesk dropoff list management'
        end
      end

      context 'when the borrower has an active credit freeze' do
        def dropoff_list_map_key = :post_offer_dropoff_list_map

        before { loan_detail.update(credit_freeze_active: true) }

        let(:loan_app_status) { app_status_for('BASIC_INFO_COMPLETE') } # only apps in BASIC_INFO_COMPLETE have credit freeze active

        include_examples 'talkdesk dropoff list management'
      end

      context 'when the loan app status is not a valid dropoff status' do
        let(:loan_app_status) { app_status_for('ONBOARDED') }
        it 'raises error' do
          expect { subject.perform(loan.id, tracking_tag) }.to raise_error(Talkdesk::AddToDropoffListJob::InvalidDropoffStatusError, "Not a valid drop off status: #{loan_app_status.name}")
        end
      end

      context 'when Talkdesk API returns BadRequestError' do
        let(:bad_request_error) { Clients::TalkdeskApi::BadRequestError.new(Faraday::BadRequestError.new) }
        let(:loan_app_status) { app_status_for('BASIC_INFO_COMPLETE') }

        before do
          loan.update(updated_at: 15.minutes.ago)
          allow(talkdesk_api_double).to receive(:add_record_to_list).and_raise(bad_request_error)
          allow(bad_request_error).to receive(:response_status).and_return(400)
          allow(bad_request_error).to receive(:response_body).and_return(response_body)
        end

        context 'when the error is due to duplicate phone number' do
          let(:response_body) do
            {
              'code' => '0530930',
              'message' => 'A Record with the same Phone Number already exists.'
            }
          end

          it 'logs the error without raising' do
            expect { subject.perform(loan.id, tracking_tag) }.not_to raise_error
            expect(Rails.logger)
              .to have_received(:error)
              .with('Loan rejected by Talkdesk during dropoff list addition',
                    loan_id: loan.id,
                    request_id: loan.request_id,
                    response_status: 400,
                    response_body:,
                    action_taken: 'Ignored duplicate phone number error',
                    talkdesk_list_id: expected_talkdesk_id,
                    tracking_tag:,
                    tags: %w[talkdesk dropoff_list])
          end
        end

        context 'when talkdesk is unable to find a suitable time zone and returns an error' do
          let(:response_body) do
            {
              'code' => '0530940',
              'message' => 'Unable to find suitable timezone.'
            }
          end

          it 'logs an error before retrying' do
            expect { subject.perform(loan.id, tracking_tag) }.to raise_error(bad_request_error)

            expect(Rails.logger)
              .to have_received(:error)
              .with('Loan rejected by Talkdesk during dropoff list addition',
                    loan_id: loan.id,
                    request_id: loan.request_id,
                    response_status: 400,
                    response_body:,
                    action_taken: 'Retrying Talkdesk call with a default timezone (America/Los_Angeles)',
                    talkdesk_list_id: expected_talkdesk_id,
                    tracking_tag:,
                    tags: %w[talkdesk dropoff_list])
          end

          it 'retries talk desk call once with timezone in params set to (America/Los_Angeles)' do
            expect { subject.perform(loan.id, tracking_tag) }.to raise_error(bad_request_error)

            call_count = 1
            expect(talkdesk_api_double).to have_received(:add_record_to_list).twice do |_talkdesk_id, params, _extra_data|
              # first call without any set timezone
              expect(params[:timezone]).to be_nil if call_count == 1
              # retry with TZ after the error
              expect(params[:timezone]).to eq('America/Los_Angeles') if call_count == 2
              call_count += 1
            end
          end

          it 'logs and re-raises the error after a retry' do
            expect { subject.perform(loan.id, tracking_tag) }.to raise_error(bad_request_error)
            expect(Rails.logger)
              .to have_received(:error)
              .with('Loan rejected by Talkdesk during dropoff list addition',
                    loan_id: loan.id,
                    request_id: loan.request_id,
                    response_status: 400,
                    response_body:,
                    action_taken: 'Raising exception',
                    talkdesk_list_id: expected_talkdesk_id,
                    tracking_tag:,
                    tags: %w[talkdesk dropoff_list])
          end
        end

        context 'when the error has any other code' do
          let(:response_body) do
            {
              'code' => '000000',
              'message' => 'Other error'
            }
          end

          it 'logs and re-raises the error' do
            expect { subject.perform(loan.id, tracking_tag) }.to raise_error(bad_request_error)
            expect(Rails.logger)
              .to have_received(:error)
              .with('Loan rejected by Talkdesk during dropoff list addition',
                    loan_id: loan.id,
                    request_id: loan.request_id,
                    response_status: 400,
                    response_body:,
                    action_taken: 'Raising exception',
                    talkdesk_list_id: expected_talkdesk_id,
                    tracking_tag:,
                    tags: %w[talkdesk dropoff_list])
          end

          it 'does not create a TalkdeskEvent record' do
            expect { subject.perform(loan.id, tracking_tag) }.to raise_error(bad_request_error)
            expect(TalkdeskEvent.count).to eq(0)
          end
        end
      end

      context 'when there is an error creating the talkdesk record' do
        before do
          allow(TalkdeskEvent).to receive(:create!).and_raise(ActiveRecord::ActiveRecordError)
        end

        it 'logs the error without raising' do
          loan.update(updated_at: 10.minutes.ago, loan_app_status: app_status_for(VALID_ADD_TO_DROPOFF_STATUSES.sample))

          subject.perform(loan.id, tracking_tag)

          expect(Rails.logger)
            .to have_received(:error)
            .with('Error creating TalkdeskEvent',
                  tags: %w[talkdesk dropoff_list],
                  unified_id: loan.unified_id,
                  loan_id: loan.id,
                  loan_app_status: loan.loan_app_status.name,
                  tracking_tag:,
                  exception: ActiveRecord::ActiveRecordError)
        end
      end
    end
  end
end
