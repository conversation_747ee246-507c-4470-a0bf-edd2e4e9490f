# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Admin::Noaas::SearchFormComponent, type: :component do
  let(:form_model) do
    Admin::NoaaSearchFormModel.new(
      unified_id: 'abc123'
    )
  end

  it 'renders the form' do
    rendered_component = render_inline(described_class.new(form_model:))
    expect(rendered_component).to have_selector('input[name="admin_noaa_search_form_model[unified_id]"][value="abc123"]')
  end
end
