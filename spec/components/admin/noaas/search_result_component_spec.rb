# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Admin::Noaas::SearchResultComponent, type: :component do
  let(:noaa) do
    {
      noaa_document_name: 'noaa document',
      noaa_created_at: '2022-01-01'.to_datetime,
      loan_status_reached_at: '2022-01-02'.to_datetime,
      noaa_download_url: '/download'
    }
  end

  it 'renders the noaa details correctly' do
    rendered_component = render_inline(described_class.new(noaa:))

    expect(rendered_component.text).to have_content('noaa document')
    expect(rendered_component.text).to have_content('January 01, 2022 00:00:00')
    expect(rendered_component.text).to have_content('January 02, 2022 00:00:00')
    expect(rendered_component).to have_selector('a[href="/download"]')
  end
end
